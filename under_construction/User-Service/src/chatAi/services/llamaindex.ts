interface LlamaIndexResponse {
  id: string;
  status?: string;
  error?: string;
}

interface RetrieveResponse {
  nodes: Array<{
    text: string;
    metadata: {
      page?: number;
      source?: string;
      file_name?: string;
    };
    score: number;
  }>;
}

export class LlamaIndexService {
  private apiKey: string;
  private baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
  private isConfigured: boolean;

  constructor() {
    this.apiKey =
      process.env.LLAMA_CLOUD_API_KEY || process.env.LLAMACLOUD_API_KEY || '';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      console.warn(
        'WARNING: LLAMA_CLOUD_API_KEY not configured. Document indexing will be disabled.',
      );
      console.warn(
        'For full functionality, please set LLAMA_CLOUD_API_KEY in Railway environment variables.',
      );
    } else {
      console.log('LlamaIndexService initialized successfully');
    }
  }

  private checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error(
        'Document indexing service is currently unavailable. Please try again later.',
      );
    }
  }

  /**
   * Sanitizes third-party API errors to prevent exposing sensitive information
   */
  private sanitizeError(error: any, context: string): Error {
    // Log the actual error for debugging
    console.error(`LlamaIndex ${context} error:`, error);

    // Return a generic user-friendly message
    if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
      return new Error(
        'Document indexing is taking longer than expected. Please try again.',
      );
    }

    if (error?.message?.includes('401') || error?.message?.includes('403')) {
      return new Error(
        'Document indexing service is temporarily unavailable. Please try again later.',
      );
    }

    if (error?.message?.includes('429')) {
      return new Error(
        'Document indexing service is busy. Please try again in a few moments.',
      );
    }

    // Generic fallback message
    return new Error('Unable to index document. Please try again.');
  }

  // ==================== Index Management ====================

  async createIndex(
    parsedData: any,
    documentName: string,
  ): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: `index_${documentName}_${Date.now()}`,
        documents: [
          {
            text: parsedData.text,
            metadata: {
              filename: documentName,
              page_count: parsedData.metadata?.page_count || 0,
              word_count: parsedData.metadata?.word_count || 0,
            },
          },
        ],
      };

      const response = await fetch(`${this.baseUrl}/indexes`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createIndex error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'createIndex',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'createIndex');
    }
  }

  async retrieve(indexId: string, query: string): Promise<RetrieveResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        query,
        top_k: 5,
      };

      const response = await fetch(
        `${this.baseUrl}/indexes/${indexId}/retrieve`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex retrieve error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'retrieve',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'retrieve');
    }
  }

  async deleteIndex(indexId: string): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/indexes/${indexId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex deleteIndex error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'deleteIndex',
        );
      }

      return {
        id: indexId,
        status: 'DELETED',
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'deleteIndex');
    }
  }
}

export const llamaIndexService = new LlamaIndexService();
