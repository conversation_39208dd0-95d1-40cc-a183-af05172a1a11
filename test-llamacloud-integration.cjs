const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, 'under_construction/User-Service/.env') });

// Import the actual services
const { llamaParseService } = require('./under_construction/User-Service/dist/chatAi/services/llamaparse.js');
const { llamaIndexService } = require('./under_construction/User-Service/dist/chatAi/services/llamaindex.js');

async function testLlamaCloudIntegration() {
  console.log('🧪 Testing LlamaCloud Integration with Real Services\n');

  // Check environment variables
  console.log('🔧 Environment Check:');
  console.log(`   LLAMA_CLOUD_API_KEY: ${process.env.LLAMA_CLOUD_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`   OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ Set' : '❌ Missing'}\n`);

  if (!process.env.LLAMA_CLOUD_API_KEY) {
    console.log('❌ LLAMA_CLOUD_API_KEY is required for this test');
    return;
  }

  try {
    // Read test file
    const testFilePath = path.join(__dirname, 'test-document.txt');
    const fileBuffer = fs.readFileSync(testFilePath);
    const filename = 'test-document.txt';

    console.log('📄 Test File Info:');
    console.log(`   File: ${filename}`);
    console.log(`   Size: ${fileBuffer.length} bytes`);
    console.log(`   Type: text/plain\n`);

    // Test 1: LlamaParse Service
    console.log('1️⃣ Testing LlamaParse Service...');

    // For text files, we should process directly, but let's test LlamaParse anyway
    console.log('   🔄 Parsing document with LlamaParse...');

    const parseResult = await llamaParseService.parseFile(fileBuffer, filename);

    if (parseResult.status === 'SUCCESS' && parseResult.result) {
      console.log('   ✅ LlamaParse SUCCESS');
      console.log(`   📊 Pages: ${parseResult.result.metadata.page_count}`);
      console.log(`   📊 Words: ${parseResult.result.metadata.word_count}`);
      console.log(`   📝 Content preview: "${parseResult.result.text.substring(0, 100)}..."`);

      // Test 2: LlamaIndex Service
      console.log('\n2️⃣ Testing LlamaIndex Service...');
      console.log('   🔄 Creating vector index...');

      const indexResult = await llamaIndexService.createIndex(parseResult.result, filename);

      if (indexResult.id) {
        console.log('   ✅ LlamaIndex SUCCESS');
        console.log(`   🎯 Index ID: ${indexResult.id}`);

        // Test 3: Vector Retrieval
        console.log('\n3️⃣ Testing Vector Retrieval...');
        console.log('   🔄 Testing retrieval with query...');

        const testQuery = "What is artificial intelligence?";
        const retrieveResult = await llamaIndexService.retrieve(indexResult.id, testQuery);

        if (retrieveResult.nodes && retrieveResult.nodes.length > 0) {
          console.log('   ✅ Vector Retrieval SUCCESS');
          console.log(`   📊 Retrieved ${retrieveResult.nodes.length} relevant chunks`);
          console.log(`   🎯 Top result score: ${retrieveResult.nodes[0].score}`);
          console.log(`   📝 Top result preview: "${retrieveResult.nodes[0].text.substring(0, 150)}..."`);
        } else {
          console.log('   ⚠️  Vector Retrieval returned no results');
        }

        // Test 4: Cleanup
        console.log('\n4️⃣ Testing Index Cleanup...');
        console.log('   🔄 Deleting test index...');

        const deleteResult = await llamaIndexService.deleteIndex(indexResult.id);
        console.log('   ✅ Index cleanup completed');

      } else {
        console.log('   ❌ LlamaIndex FAILED - No index ID returned');
        console.log('   📋 Response:', indexResult);
      }

    } else {
      console.log('   ❌ LlamaParse FAILED');
      console.log('   📋 Response:', parseResult);
    }

    console.log('\n🎉 LlamaCloud Integration Test Complete!');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('📋 Full error:', error);
  }
}

// Test direct text processing (our implementation)
async function testDirectTextProcessing() {
  console.log('\n📝 Testing Direct Text Processing (Our Implementation)...');

  try {
    const testFilePath = path.join(__dirname, 'test-document.txt');
    const fileBuffer = fs.readFileSync(testFilePath);
    const filename = 'test-document.txt';

    // Process as text file (like our implementation does)
    const fileExtension = filename.toLowerCase().split('.').pop();
    const textExtensions = ['txt', 'csv', 'html', 'htm', 'md', 'xml'];

    if (textExtensions.includes(fileExtension || '')) {
      const parsedText = fileBuffer.toString('utf-8');
      const wordCount = parsedText.split(/\s+/).filter(word => word.length > 0).length;

      const parsedData = {
        text: parsedText,
        metadata: {
          page_count: 1,
          word_count: wordCount,
        },
      };

      console.log('   ✅ Direct text processing SUCCESS');
      console.log(`   📊 Word count: ${wordCount}`);
      console.log(`   📄 Content length: ${parsedText.length} characters`);

      // Test indexing with our processed data
      console.log('   🔄 Creating index with processed text...');

      const indexResult = await llamaIndexService.createIndex(parsedData, filename);

      if (indexResult.id) {
        console.log('   ✅ Indexing SUCCESS');
        console.log(`   🎯 Index ID: ${indexResult.id}`);

        // Cleanup
        await llamaIndexService.deleteIndex(indexResult.id);
        console.log('   ✅ Cleanup completed');
      } else {
        console.log('   ❌ Indexing FAILED');
      }
    }

  } catch (error) {
    console.error('   ❌ Direct text processing failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testLlamaCloudIntegration();
  await testDirectTextProcessing();
}

runAllTests().catch(console.error);
