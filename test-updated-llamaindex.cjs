const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, 'under_construction/User-Service/.env') });

// Import the updated service
const { llamaIndexService } = require('./under_construction/User-Service/dist/chatAi/services/llamaindex.js');

async function testUpdatedLlamaIndexService() {
  console.log('🧪 Testing Updated LlamaIndex Service with New API\n');

  // Check environment variables
  console.log('🔧 Environment Check:');
  console.log(`   LLAMA_CLOUD_API_KEY: ${process.env.LLAMA_CLOUD_API_KEY ? '✅ Set' : '❌ Missing'}\n`);

  if (!process.env.LLAMA_CLOUD_API_KEY) {
    console.log('❌ LLAMA_CLOUD_API_KEY is required for this test');
    return;
  }

  try {
    // Read test file
    const testFilePath = path.join(__dirname, 'test-document.txt');
    const fileBuffer = fs.readFileSync(testFilePath);
    const filename = 'test-document.txt';

    console.log('📄 Test File Info:');
    console.log(`   File: ${filename}`);
    console.log(`   Size: ${fileBuffer.length} bytes`);
    console.log(`   Type: text/plain\n`);

    // Prepare parsed data (simulating our document processing)
    const parsedText = fileBuffer.toString('utf-8');
    const wordCount = parsedText.split(/\s+/).filter(word => word.length > 0).length;
    
    const parsedData = {
      text: parsedText,
      metadata: {
        page_count: 1,
        word_count: wordCount,
      },
    };

    console.log('📊 Parsed Data:');
    console.log(`   Word count: ${wordCount}`);
    console.log(`   Content length: ${parsedText.length} characters\n`);

    // Test 1: Create Index (Project + File + Pipeline + Retriever)
    console.log('1️⃣ Testing Complete Index Creation...');
    console.log('   🔄 Creating project, uploading file, creating pipeline and retriever...');
    
    const indexResult = await llamaIndexService.createIndex(parsedData, filename);
    
    if (indexResult.id && indexResult.status === 'SUCCESS') {
      console.log('   ✅ Index Creation SUCCESS');
      console.log(`   🎯 Retriever ID: ${indexResult.id}`);
      
      // Test 2: Vector Retrieval
      console.log('\n2️⃣ Testing Vector Retrieval...');
      console.log('   🔄 Testing retrieval with query...');
      
      const testQuery = "What is ChatAI?";
      const retrieveResult = await llamaIndexService.retrieve(indexResult.id, testQuery);
      
      if (retrieveResult.nodes && retrieveResult.nodes.length > 0) {
        console.log('   ✅ Vector Retrieval SUCCESS');
        console.log(`   📊 Retrieved ${retrieveResult.nodes.length} relevant chunks`);
        console.log(`   🎯 Top result score: ${retrieveResult.nodes[0].score}`);
        console.log(`   📝 Top result preview: "${retrieveResult.nodes[0].text.substring(0, 150)}..."`);
      } else {
        console.log('   ⚠️  Vector Retrieval returned no results');
        console.log('   📋 Response:', retrieveResult);
      }
      
      // Test 3: Cleanup
      console.log('\n3️⃣ Testing Cleanup...');
      console.log('   🔄 Deleting retriever...');
      
      const deleteResult = await llamaIndexService.deleteIndex(indexResult.id);
      if (deleteResult.status === 'DELETED') {
        console.log('   ✅ Cleanup completed successfully');
      } else {
        console.log('   ⚠️  Cleanup may have failed');
        console.log('   📋 Response:', deleteResult);
      }
      
    } else {
      console.log('   ❌ Index Creation FAILED');
      console.log('   📋 Response:', indexResult);
    }

    console.log('\n🎉 Updated LlamaIndex Service Test Complete!');
    
  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('📋 Full error:', error);
    
    // Check if it's a specific API error
    if (error.message.includes('404')) {
      console.log('\n💡 Possible Issues:');
      console.log('   - API endpoint might still be incorrect');
      console.log('   - LlamaCloud API might have changed again');
      console.log('   - API key might not have the right permissions');
    } else if (error.message.includes('401') || error.message.includes('403')) {
      console.log('\n💡 Possible Issues:');
      console.log('   - API key might be invalid or expired');
      console.log('   - API key might not have the right permissions');
    } else if (error.message.includes('FormData')) {
      console.log('\n💡 Possible Issues:');
      console.log('   - FormData might not be available in Node.js environment');
      console.log('   - Need to install form-data package or use different approach');
    }
  }
}

// Test individual components
async function testIndividualComponents() {
  console.log('\n🔧 Testing Individual Components...\n');
  
  try {
    // Test 1: Project Creation
    console.log('1️⃣ Testing Project Creation...');
    const project = await llamaIndexService.createProject('Test-Project-' + Date.now());
    console.log('   ✅ Project created:', project.id);
    
    // Test 2: File Upload
    console.log('\n2️⃣ Testing File Upload...');
    const testContent = 'This is a test document for LlamaCloud API testing.';
    const fileBuffer = Buffer.from(testContent, 'utf-8');
    const file = await llamaIndexService.uploadFile(fileBuffer, 'test.txt', project.id);
    console.log('   ✅ File uploaded:', file.id);
    
    // Test 3: Pipeline Creation
    console.log('\n3️⃣ Testing Pipeline Creation...');
    const pipeline = await llamaIndexService.createPipeline('test-pipeline', project.id, [file.id]);
    console.log('   ✅ Pipeline created:', pipeline.id);
    
    // Test 4: Retriever Creation
    console.log('\n4️⃣ Testing Retriever Creation...');
    const retriever = await llamaIndexService.createRetriever('test-retriever', project.id, [pipeline.id]);
    console.log('   ✅ Retriever created:', retriever.id);
    
    console.log('\n🎉 All individual components working!');
    
  } catch (error) {
    console.error('\n❌ Individual component test failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testUpdatedLlamaIndexService();
  await testIndividualComponents();
}

runAllTests().catch(console.error);
