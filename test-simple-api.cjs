// Simple API test to debug the issue
async function testSimpleAPI() {
  console.log('🧪 Testing Simple API Calls\n');

  const baseUrl = 'http://localhost:3000';

  try {
    // Test 1: Basic health check
    console.log('1️⃣ Testing basic health check...');
    const healthResponse = await fetch(`${baseUrl}/users`);
    console.log(`   Status: ${healthResponse.status}`);

    if (healthResponse.ok) {
      const healthData = await healthResponse.text();
      console.log(`   ✅ Health check successful: ${healthData}`);
    } else {
      console.log(`   ❌ Health check failed: ${healthResponse.statusText}`);
    }

    // Test 2: Login
    console.log('\n2️⃣ Testing login...');
    const loginResponse = await fetch(`${baseUrl}/users/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Demo123!'
      })
    });

    console.log(`   Login status: ${loginResponse.status}`);

    if (!loginResponse.ok) {
      const loginError = await loginResponse.text();
      console.log(`   ❌ Login failed: ${loginError}`);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('   📋 Login response:', JSON.stringify(loginData, null, 2));

    const token = loginData.result?.authToken || loginData.result?.token || loginData.token;
    if (!token) {
      console.log('   ❌ No token found in response');
      return;
    }

    console.log('   ✅ Login successful');
    console.log(`   🔑 Token: ${token.substring(0, 20)}...`);

    // Test 3: Get apps with detailed error handling
    console.log('\n3️⃣ Testing get apps...');
    const appsResponse = await fetch(`${baseUrl}/users/app/get-apps`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Apps status: ${appsResponse.status}`);
    console.log(`   Apps headers: ${JSON.stringify([...appsResponse.headers.entries()])}`);

    if (appsResponse.ok) {
      const appsData = await appsResponse.json();
      console.log('   ✅ Get apps successful');
      console.log(`   📊 Apps count: ${appsData.result?.length || 0}`);

      if (appsData.result && appsData.result.length > 0) {
        console.log(`   📋 First app: ${JSON.stringify(appsData.result[0], null, 2)}`);
      }
    } else {
      const appsError = await appsResponse.text();
      console.log(`   ❌ Get apps failed: ${appsError}`);
    }

    // Test 4: Try alternative endpoint
    console.log('\n4️⃣ Testing alternative get-all-apps endpoint...');
    const allAppsResponse = await fetch(`${baseUrl}/users/app/get-all-apps`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   All apps status: ${allAppsResponse.status}`);

    if (allAppsResponse.ok) {
      const allAppsData = await allAppsResponse.json();
      console.log('   ✅ Get all apps successful');
      console.log(`   📊 Apps count: ${allAppsData.result?.length || 0}`);

      if (allAppsData.result && allAppsData.result.length > 0) {
        console.log(`   📋 First app: ${JSON.stringify(allAppsData.result[0], null, 2)}`);
      }
    } else {
      const allAppsError = await allAppsResponse.text();
      console.log(`   ❌ Get all apps failed: ${allAppsError}`);
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  }
}

// Run the test
testSimpleAPI().catch(console.error);
