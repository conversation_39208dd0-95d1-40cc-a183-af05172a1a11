const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test the actual API endpoints
async function testDocumentUploadAPI() {
  console.log('🧪 Testing ChatAI Document Upload API\n');

  const baseUrl = 'http://localhost:3000';
  
  // Step 1: Login to get JWT token
  console.log('1️⃣ Logging in to get JWT token...');
  
  const loginResponse = await fetch(`${baseUrl}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Demo123!'
    })
  });

  if (!loginResponse.ok) {
    console.log('❌ Login failed - need to register user first');
    
    // Try to register the user
    console.log('   🔄 Registering demo user...');
    const registerResponse = await fetch(`${baseUrl}/users/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Demo',
        lastName: 'User',
        email: '<EMAIL>',
        username: 'demouser',
        password: 'Demo123!'
      })
    });

    if (!registerResponse.ok) {
      const registerError = await registerResponse.text();
      console.log('❌ Registration failed:', registerError);
      return;
    }

    console.log('   ✅ User registered successfully');
    
    // Try login again
    const loginResponse2 = await fetch(`${baseUrl}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Demo123!'
      })
    });

    if (!loginResponse2.ok) {
      console.log('❌ Login still failed after registration');
      return;
    }

    const loginData2 = await loginResponse2.json();
    console.log('   ✅ Login successful');
    var token = loginData2.result.token;
  } else {
    const loginData = await loginResponse.json();
    console.log('   ✅ Login successful');
    var token = loginData.result.token;
  }

  // Step 2: Create an application
  console.log('\n2️⃣ Creating application...');
  
  const appResponse = await fetch(`${baseUrl}/users/app/create-app`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      appName: 'Test ChatAI App',
      description: 'Test application for ChatAI document upload'
    })
  });

  if (!appResponse.ok) {
    const appError = await appResponse.text();
    console.log('❌ App creation failed:', appError);
    return;
  }

  const appData = await appResponse.json();
  console.log('   ✅ Application created');
  const appId = appData.result.id;

  // Step 3: Setup ChatAI
  console.log('\n3️⃣ Setting up ChatAI...');
  
  const chatAiResponse = await fetch(`${baseUrl}/users/app/chatai/setup-chatai`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      name: 'Test ChatAI Project',
      description: 'Test project for document upload',
      appId: appId
    })
  });

  if (!chatAiResponse.ok) {
    const chatAiError = await chatAiResponse.text();
    console.log('❌ ChatAI setup failed:', chatAiError);
    return;
  }

  console.log('   ✅ ChatAI project created');

  // Step 4: Check credit usage before upload
  console.log('\n4️⃣ Checking credit usage...');
  
  const creditResponse = await fetch(`${baseUrl}/users/app/chatai/get-credit-usage?appId=${appId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (creditResponse.ok) {
    const creditData = await creditResponse.json();
    console.log('   ✅ Credit check successful');
    console.log(`   💳 Credits remaining: ${creditData.result.creditsRemaining}`);
    console.log(`   📋 Subscription: ${creditData.result.subscriptionStatus}`);
  }

  // Step 5: Upload document
  console.log('\n5️⃣ Uploading document...');
  
  const testFilePath = path.join(__dirname, 'test-document.txt');
  const fileBuffer = fs.readFileSync(testFilePath);
  
  const formData = new FormData();
  formData.append('file', fileBuffer, 'test-document.txt');
  formData.append('appId', appId);
  formData.append('title', 'Test Document');
  formData.append('description', 'Test document for upload pipeline');

  const uploadResponse = await fetch(`${baseUrl}/users/app/chatai/upload-document`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      ...formData.getHeaders()
    },
    body: formData
  });

  if (!uploadResponse.ok) {
    const uploadError = await uploadResponse.text();
    console.log('❌ Document upload failed:', uploadError);
    return;
  }

  const uploadData = await uploadResponse.json();
  console.log('   ✅ Document upload started');
  console.log(`   📄 Document ID: ${uploadData.result.documentId}`);
  console.log(`   📊 Status: ${uploadData.result.status}`);

  // Step 6: Monitor document processing
  console.log('\n6️⃣ Monitoring document processing...');
  
  let attempts = 0;
  const maxAttempts = 20; // 2 minutes max
  
  while (attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 6000)); // Wait 6 seconds
    
    const documentsResponse = await fetch(`${baseUrl}/users/app/chatai/get-documents?appId=${appId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (documentsResponse.ok) {
      const documentsData = await documentsResponse.json();
      const document = documentsData.result.find(doc => doc.id === uploadData.result.documentId);
      
      if (document) {
        console.log(`   🔄 Attempt ${attempts + 1}: Status = ${document.status}`);
        
        if (document.status === 'ready') {
          console.log('   ✅ Document processing completed!');
          console.log(`   📊 Pages: ${document.pageCount}`);
          console.log(`   📊 Words: ${document.wordCount}`);
          console.log(`   🎯 Index ID: ${document.indexId}`);
          break;
        } else if (document.status === 'error') {
          console.log('   ❌ Document processing failed');
          console.log(`   📋 Error: ${document.errorMessage}`);
          break;
        }
      }
    }
    
    attempts++;
  }

  if (attempts >= maxAttempts) {
    console.log('   ⚠️  Document processing timeout - still in progress');
  }

  // Step 7: Check final credit usage
  console.log('\n7️⃣ Checking final credit usage...');
  
  const finalCreditResponse = await fetch(`${baseUrl}/users/app/chatai/get-credit-usage?appId=${appId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (finalCreditResponse.ok) {
    const finalCreditData = await finalCreditResponse.json();
    console.log('   ✅ Final credit check');
    console.log(`   💳 Credits remaining: ${finalCreditData.result.creditsRemaining}`);
    console.log(`   📊 Usage records: ${finalCreditData.result.usage.length}`);
  }

  console.log('\n🎉 Document Upload API Test Complete!');
}

// Run the test
testDocumentUploadAPI().catch(console.error);
